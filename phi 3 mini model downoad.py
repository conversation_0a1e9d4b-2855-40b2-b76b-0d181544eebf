from huggingface_hub import snapshot_download
import os

# Configuration
model_id = "microsoft/phi-3-mini-4k-instruct"
local_dir = "./phi-3-mini"
os.makedirs(local_dir, exist_ok=True)

# Download the model (requires Hugging Face account)
snapshot_download(
    repo_id=model_id,
    local_dir=local_dir,
    token="*************************************",  # Get from huggingface.co/settings/tokens
    local_dir_use_symlinks=False,
    revision="main"
)

print(f"Model downloaded to: {local_dir}")