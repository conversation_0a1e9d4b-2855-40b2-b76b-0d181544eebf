# Phi-3 Mini Chat Troubleshooting Guide

## Common Issues and Solutions

### 1. Model Loading Errors
**Error**: "Error loading model: ..."
**Solutions**:
- Ensure you have at least 8GB of free RAM
- Check that all model files are present in the `phi-3-mini` directory
- Verify the model path is correct
- Install/update transformers: `pip install --upgrade transformers torch`

### 2. Out of Memory Errors
**Error**: "CUDA out of memory" or similar
**Solutions**:
- The script is configured for CPU usage, but if you see memory errors:
- Close other applications to free up RAM
- Reduce `max_new_tokens` in the generation parameters
- Restart your computer to clear memory

### 3. Slow Response Times
**Expected**: First response may take 30-60 seconds on CPU
**To improve**:
- Reduce `max_new_tokens` from 200 to 100
- Increase `torch.set_num_threads()` if you have more CPU cores
- Consider using GPU if available

### 4. Poor Response Quality
**Solutions**:
- Adjust temperature (0.1-1.0, lower = more focused)
- Modify top_p (0.1-1.0, lower = more conservative)
- Try different prompting styles

### 5. Installation Issues
**Required packages**:
```bash
pip install transformers torch tokenizers
```

### 6. Model Files Missing
**Check these files exist in phi-3-mini directory**:
- config.json
- tokenizer.json
- model-*.safetensors files
- tokenizer_config.json

## Performance Tips
- First response is always slower (model loading)
- Subsequent responses should be faster
- CPU inference is slower than GPU but more compatible
- Close unnecessary applications for better performance
