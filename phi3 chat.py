from transformers import AutoModelForCausalLM, AutoTokenizer
import torch

# CPU configuration
torch.set_num_threads(4)
model_path = r"C:\Users\<USER>\Desktop\Phi 3 mini\phi-3-mini"

try:
    model = AutoModelForCausalLM.from_pretrained(
        model_path,
        device_map="cpu",
        trust_remote_code=True,
        torch_dtype=torch.float32,
        attn_implementation="eager"  # Disable flash attention
    )
    tokenizer = AutoTokenizer.from_pretrained(model_path)
except Exception as e:
    print(f"Error loading model: {str(e)}")
    exit()

def format_response(full_text):
    return full_text.split("<|assistant|>")[-1].strip()

print("Phi-3 Chat (CPU Mode)\nType 'exit' to quit\n" + "-"*30)

while True:
    try:
        user_input = input("You: ")
        if user_input.lower() in ['exit', 'quit']:
            break
            
        prompt = f"<|user|>\n{user_input}<|end|>\n<|assistant|>\n"
        inputs = tokenizer(prompt, return_tensors="pt", max_length=512, truncation=True)
        
        outputs = model.generate(
            inputs.input_ids,
            max_new_tokens=150,
            temperature=0.7,
            top_p=0.9,
            repetition_penalty=1.1,
            pad_token_id=tokenizer.eos_token_id
        )
        
        response = tokenizer.decode(outputs[0], skip_special_tokens=True)
        print(f"\nPhi-3: {format_response(response)}\n{'-'*50}\n")
        
    except Exception as e:
        print(f"Generation error: {str(e)}")

print("Chat session ended.")