from transformers import AutoModelForCausalLM, AutoTokenizer
import torch
import os

# CPU configuration
torch.set_num_threads(4)
model_path = r"C:\Users\<USER>\Desktop\Phi 3 mini\phi-3-mini"

# Check if model path exists
if not os.path.exists(model_path):
    print(f"Error: Model path does not exist: {model_path}")
    print("Please check the path and try again.")
    exit()

print("Loading Phi-3 mini model... This may take a moment.")

try:
    model = AutoModelForCausalLM.from_pretrained(
        model_path,
        device_map="cpu",
        trust_remote_code=True,
        torch_dtype=torch.float32,
        attn_implementation="eager"  # Disable flash attention
    )
    tokenizer = AutoTokenizer.from_pretrained(model_path)

    # Set pad token if not already set
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token

    print("✓ Model loaded successfully!")

except Exception as e:
    print(f"Error loading model: {str(e)}")
    print("\nTroubleshooting tips:")
    print("1. Make sure you have enough RAM (model requires ~8GB)")
    print("2. Check if all model files are present in the directory")
    print("3. Ensure transformers library is installed: pip install transformers")
    exit()

def format_response(full_text):
    """Extract the assistant's response from the full generated text."""
    if "<|assistant|>" in full_text:
        return full_text.split("<|assistant|>")[-1].strip()
    return full_text.strip()

def chat_loop():
    """Main chat loop."""
    print("\n" + "="*60)
    print("🤖 Phi-3 Mini Chat Interface")
    print("="*60)
    print("Commands:")
    print("  - Type your message and press Enter")
    print("  - Type 'exit', 'quit', or 'q' to end the session")
    print("  - Type 'clear' to clear the screen")
    print("  - Type 'help' for this help message")
    print("-"*60)

    conversation_history = []

    while True:
        try:
            user_input = input("\n💬 You: ").strip()

            if user_input.lower() in ['exit', 'quit', 'q']:
                print("\n👋 Goodbye! Chat session ended.")
                break
            elif user_input.lower() == 'clear':
                os.system('cls' if os.name == 'nt' else 'clear')
                continue
            elif user_input.lower() == 'help':
                print("\nCommands:")
                print("  - exit/quit/q: End the session")
                print("  - clear: Clear the screen")
                print("  - help: Show this help message")
                continue
            elif not user_input:
                print("Please enter a message or type 'help' for commands.")
                continue

            # Add user input to conversation history
            conversation_history.append(f"User: {user_input}")

            print("🤔 Thinking...")

            # Format prompt using Phi-3's chat template
            prompt = f"<|user|>\n{user_input}<|end|>\n<|assistant|>\n"

            # Tokenize input
            inputs = tokenizer(
                prompt,
                return_tensors="pt",
                max_length=512,
                truncation=True,
                padding=True
            )

            # Generate response
            with torch.no_grad():  # Save memory
                outputs = model.generate(
                    inputs.input_ids,
                    max_new_tokens=200,
                    temperature=0.7,
                    top_p=0.9,
                    repetition_penalty=1.1,
                    pad_token_id=tokenizer.pad_token_id,
                    eos_token_id=tokenizer.eos_token_id,
                    do_sample=True
                )

            # Decode and format response
            full_response = tokenizer.decode(outputs[0], skip_special_tokens=True)
            assistant_response = format_response(full_response)

            # Add to conversation history
            conversation_history.append(f"Assistant: {assistant_response}")

            print(f"\n🤖 Phi-3: {assistant_response}")
            print("-"*60)

        except KeyboardInterrupt:
            print("\n\n⚠️  Interrupted by user. Type 'exit' to quit properly.")
        except Exception as e:
            print(f"\n❌ Generation error: {str(e)}")
            print("Please try again or type 'exit' to quit.")

if __name__ == "__main__":
    chat_loop()